// 创建人：李季
// 创建日期：2025-01-08
// 创建人工号：138693
// 创建人邮箱：<EMAIL>
const event = require("../../pages/SpecialEvent_page")
const basepage = require("../../tools/basepage")

Feature('权益中心-专题活动');

Before(async ({ login }) => {
  await login('Testzhongtai');
});

Scenario('专题活动-创建专题-顶部广告', async ({ I }) => {
  await event.CreatEvent("顶部广告")
});

Scenario('专题活动-创建专题-锚点TAB', async ({ I }) => {
  await event.CreatEvent("锚点TAB")
});

Scenario('专题活动-创建专题-合作商品', async ({ I }) => {
  await event.CreatEvent("合作商品")
});

Scenario('专题活动-创建专题-图片', async ({ I }) => {
  await event.CreatEvent("图片")
});

Scenario('专题活动-查询', async ({ I }) => {
  await event.SearchEvent()
});

// 创建日期：2025-07-04
Scenario('专题活动-编辑', async ({ I }) => {
  await event.SearchEvent()
  await event.EditEvent()
});

Scenario('专题活动-操作日志', async ({ I }) => {
  await event.SearchEvent()
  await basepage.moveCursorAndClick(I, "操作日志")
  I.waitForText('创建活动', 20)
});

// 创建日期：2025-07-04
Scenario('专题活动-投放渠道', async ({ I }) => {
  await event.SearchEvent()
  await event.DeliveryChannel()
});

Scenario('专题活动-修改状态-启用', async ({ I }) => {
  await event.SearchEvent()
  await event.EnableStatus()
});

Scenario('专题活动-修改状态-停用', async ({ I }) => {
  await event.SearchEvent()
  await event.DisableStatus()
});



