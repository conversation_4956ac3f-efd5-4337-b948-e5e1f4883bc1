const { setHeadlessWhen, setCommonPlugins } = require('@codeceptjs/configure');
// turn on headless mode when running with HEADLESS=true environment variable
// export HEADLESS=true && npx codeceptjs run
setHeadlessWhen(process.env.HEADLESS);
const codeceptjs = require('codeceptjs');

// enable all common plugins https://github.com/codeceptjs/configure#setcommonplugins
// setCommonPlugins();

/** @type {CodeceptJS.MainConfig} */
var url =  'https://t-next.gaodun.com'
exports.config = {
  tests: './tests/*/*_test.js',
  output: './output',
  REST: {
    endpoint: "https://t-apigateway.gaodun.com",
    defaultHeaders: {
      'Auth': '11111',
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
  },
  helpers: {
    Playwright: {
      browser: 'chromium',
      url: url,
      show: true,
      keepCookies:true,
      navigationTimeout: 40000,
    }
  },
  include: {
    I: './steps_file.js',
    discountPage: "./pages/discountPage.js",
  },
  name: 'goldengo-powercenter',
  plugins: {
    customLocator: {
      enabled: true,
      attribute: 'placeholder',
    },
    allure: {

    },
    pauseOnFail: {
      enabled: true,
    },
    autoLogin: {
      enabled: true,
      saveToFile: true,
      inject: 'login',
      users: {
        Testzhongtai: {
          login: (I) => {
            I.login('<EMAIL>', 'gaodun123@', '中台自动化')
          },
          check: (I) => {
            I.amOnPage('/list')
            I.waitForText('中台自动化', 20)
          },
        },
      }
    }
  }
}