const fs = require('fs');
const https = require('https');
const crypto = require('crypto');

// 生成随机订单号
function generateRandomOrderId() {
    const length = 20;
    const characters = '0123456789';
    let result = '';
    const randomBytes = crypto.randomBytes(length);
    for (let i = 0; i < length; i++) {
        result += characters[randomBytes[i] % characters.length];
    }
    return result;
}

// 构建 JSON 数据
function buildJson(p) {
    // 读取 bytedance.json 文件
    const jd = JSON.parse(fs.readFileSync('./tools/bytedance.json', 'utf-8'));

    // 更新顶级属性
    jd.shopId = p.shopId;
    jd.shopName = p.shopName;
    jd.orderId = p.orderId;
    jd.biz = p.biz;
    jd.bizDesc = p.bizDesc;
    jd.channelPaymentNo = p.channelPaymentNo;
    jd.encryptPayTel = p.phone;
    jd.encryptPostTel = p.phone;

    // 获取 skuOrderList 中的第一个元素
    let sku = jd.skuOrderList[0];
    sku.channelPaymentNo = p.channelPaymentNo;
    sku.parentOrderId = p.orderId;
    sku.productId = p.productId;
    sku.productIdStr = String(p.productId);
    sku.productName = p.productName;
    sku.encryptPostTel = p.phone;
    sku.biz = p.biz;

    // 清空 skuOrderList
    jd.skuOrderList = [];

    // 遍历 skuId 数组
    for (let i = 0; i < p.skuId.length; i++) {
        let s = JSON.parse(JSON.stringify(sku));
        s.skuId = p.skuId[i];
        s.code = p.code;
        if (p.skuId.length === 1) {
            s.orderId = p.orderId;
        } else {
            s.orderId = `${p.orderId}0${i}`;
        }
        jd.skuOrderList.push(s);
    }

    // 将更新后的 JSON 数据写入 body.json 文件
    fs.writeFileSync('./body.json', JSON.stringify(jd, null, 2), 'utf-8');
}

// 主函数
async function syncOrder_learnCard(learnCardBatchNo) {
    // 生成随机订单号
    const randomOrderId = generateRandomOrderId();
    console.log('外部订单号：', randomOrderId);

    // 请求的 URL
    const url = "https://t-supply-center.gaodunwangxiao.com/api/v1/byte-dance/sync-byte-dance-order";

    // 请求参数
    const params = {
        orderId: randomOrderId,
        channelPaymentNo: randomOrderId,
        productId: ************,
        productName: 'UI自动化抖音虚拟卡',
        shopId: 60580362,
        shopName: '高顿教育文化培训专卖店',
        biz: 11,
        bizDesc: '虚拟卡',
        skuId: [24455632446452454],
        phone: 19102734951,
        code: learnCardBatchNo
    };

    // 构建 JSON 数据
    buildJson(params);

    // 读取 body.json 文件
    const data = fs.readFileSync('./body.json', 'utf-8');

    // 请求头
    const headers = {
        "Content-Type": "application/json"
    };

    // 解析 URL
    const parsedUrl = new URL(url);

    // 创建 HTTP 请求选项
    const options = {
        hostname: parsedUrl.hostname,
        port: parsedUrl.port || 443,
        path: parsedUrl.pathname + parsedUrl.search,
        method: 'POST',
        headers: headers
    };

    // 发送 POST 请求
    return await new Promise((resolve, reject) => {
        const req = https.request(options, (res) => {
            let responseData = '';
            res.on('data', (chunk) => {
                responseData += chunk;
            });
            res.on('end', () => {
                if (res.statusCode === 200) {
                    console.log("请求成功，响应内容:", responseData);
                    resolve(responseData);
                } else {
                    console.log("请求失败，状态码:", res.statusCode);
                    reject(new Error(`Request failed with status code ${res.statusCode}`));
                }
            });
        });

        req.on('error', (error) => {
            console.error('请求发生错误:', error);
            reject(error);
        });

        // 写入请求数据
        req.write(data);
        req.end();
    });
}


module.exports = {
    syncOrder_learnCard,
}
