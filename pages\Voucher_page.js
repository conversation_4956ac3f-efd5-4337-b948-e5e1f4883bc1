// 创建人：李季
// 创建日期：2025-01-03
// 创建人工号：138693
// 创建人邮箱：<EMAIL>
const { I, } = inject();
const { random } = require('faker');
const { randomInt } = require('crypto');
const basepage = require('../tools/basepage')
const apiHelper = require("../tools/apiHelper")

const voucherConfig = {
    siteLink: 0,
    coupon: ''
}

module.exports = {
    voucherConfig,

    async CreateNewCoupons(DiscountModel = "折扣") {
        await basepage.openPage('/system/goldengo/powerCenter/discount/create')
        voucherConfig.coupon = "UI自动化" + DiscountModel + "优惠券" + Date.now()
        await basepage.moveCursorAndClick(I, '优惠券内部名称', voucherConfig.coupon)
        await basepage.moveCursorAndClick(I, '优惠券展示标题', voucherConfig.coupon)

        //优惠模式
        if (DiscountModel == "折扣") {
            await basepage.moveCursorAndClick(I, '折扣')
        } else if (DiscountModel == "满减") {
            await basepage.moveCursorAndClick(I, '满减')
            await basepage.clear(I, "满减比例", 100)
            await basepage.moveCursorAndClick(I, "满减比例", "2")
            await basepage.clear(I, " 减 ")
            await basepage.moveCursorAndClick(I, " 减 ", "1")
        } else if (DiscountModel == "立减") {
            await basepage.moveCursorAndClick(I, '立减')
        } else if (DiscountModel == "兑换") {
            await basepage.moveCursorAndClick(I, '兑换')
        } else if (DiscountModel == "联报立减") {
            await basepage.moveCursorAndClick(I, '联报立减')
            await basepage.moveCursorAndClick(I, "联报立减", "1", 76, 0, null, 2)
        }

        //使用有效期
        if (randomInt(1, 3) == 1) {
            await basepage.editTime()
        } else {
            await basepage.moveCursorAndClick(I, "领取后")
            await basepage.moveCursorAndClick(I, "领取后", "1")
        }
        await basepage.editTime(2)
        await basepage.moveCursorAndClick(I, "发放总数量", "100")

        if (randomInt(1, 3) == 2) {
            if (DiscountModel == "满减" || DiscountModel == "立减") {
                voucherConfig.siteLink = 1 //判断是否点击官网链接
                await basepage.moveCursorAndClick(I, "助力活动")
                await basepage.moveCursorAndClick(I, "助力有效期", "1")
            } else {
                await basepage.moveCursorAndClick(I, "助力活动")
                await basepage.moveCursorAndClick(I, "助力有效期", "1")
                await basepage.moveCursorAndClick(I, "保存草稿")
                I.waitForText("助力活动只支持满减券和立减券", 20)
                await basepage.moveCursorAndClick(I, "赠送")
            }
        }

        await basepage.moveCursorAndClick(I, "保存草稿")
    },

    async SearchNewCoupons() {
        await basepage.openPage('/system/goldengo/powerCenter/discount')
        await basepage.moveCursorAndClick(I, "优惠券名称", voucherConfig.coupon)
        await basepage.moveCursorAndClick(I, "查 询")
        // 1. 先获取token
        const token = await apiHelper.getAccessToken({
            loginUrl: '/api/v4/vigo/login',
            appid: '210666',
            GDSID: 'GE5JYVCI84Eaxpaby0z9rikyueckl9qdsrzd4abb441ej',
            user: '<EMAIL>',
            password: 'PwbwIeumyH19H9EWqLbuEVbZMFSP/Nn9rYvMQhRY+1wdLVHE+x4=',
        });
        //2.获取优惠券id
        const orderData = { "pageIndex": 1, "pageNum": 1, "pageSize": 10, "searchWhere": { "discountName": voucherConfig.coupon } }
        const response = await apiHelper.post(
            '/coupon/web/v2/coupon/list',  // 接口地址
            orderData,                // 请求数据
            {},
            false,                    // 是否表单数据
            token                     // 上面获取的 token
        );
        const data = response.data
        const voucherId = data.result.list[0].discountId
        const voucherType = data.result.list[0].discountType
        I.waitForText(voucherConfig.coupon, 20)
        return { voucherId, voucherType };
    },

    async EditNewCoupons() {
        await basepage.moveCursorAndClick(I, "编辑")
        await basepage.moveCursorAndClick(I, "优惠券展示副标题", "编辑" + voucherConfig.coupon, 100)
        await basepage.moveCursorAndClick(I, "保存草稿")
    },

    async ProductListNewCoupons() {
        I.wait(2)
        await this.SearchNewCoupons()
        await basepage.moveCursorAndClick(I, "产品列表")
        if (randomInt(1, 3) == 1) { //适用已选项目的全部CRM启用状态课程:是
            await basepage.moveCursorAndClick(I, "是")
            await basepage.elementOperate("//*[@class='ant-select-selection-search']").click()
            I.pressKey("Enter")
            I.pressKey("Tab")
            await basepage.moveCursorAndClick(I, "保 存")
            I.waitForText("关联成功!", 20)
        } else {
            I.wait(2)
            await basepage.elementOperate("//*[@class='ant-select-selection-search']").click()
            I.pressKey("Enter")
            I.pressKey("Tab")
            await basepage.moveCursorAndClick(I, "添加")
            await basepage.moveCursorAndClick(I, "产品名称/ID", "CP-044877")
            await basepage.moveCursorAndClick(I, "查 询")
            await basepage.elementOperate("(//*[@class='ant-checkbox-input'])[2]").click()
            await basepage.moveCursorAndClick(I, "确 定")
            await basepage.moveCursorAndClick(I, "保 存")
            I.waitForText("关联成功!", 20)
        }
    },

    async EnableStatus() {
        I.wait(2)
        await basepage.moveCursorAndClick(I, "修改状态")
        await basepage.moveCursorAndClick(I, "启用")
        await basepage.moveCursorAndClick(I, "确 定")
        I.waitForText("修改成功", 20)
    },

    async DisableStatus() {
        await basepage.moveCursorAndClick(I, "修改状态")
        await basepage.moveCursorAndClick(I, "停用")
        await basepage.moveCursorAndClick(I, "确 定")
        I.waitForText("修改成功", 20)
    },

    async DeleteCoupon() {
        await basepage.moveCursorAndClick(I, "删除")
        I.waitForText("删除成功", 20)
    },
}
