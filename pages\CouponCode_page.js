// 创建人：李季
// 创建日期：2025-01-08
// 创建人工号：138693
// 创建人邮箱：<EMAIL>
const { I, } = inject();
const apiHelper = require("../tools/apiHelper")
const basepage = require('../tools/basepage');

const baseUrl = '/system/goldengo/powerCenter/discountCode'
const codeConfig = {
    couponCode: ''
};

module.exports = {
    codeConfig,

    /**
     * 创建新的优惠码
     * @param {string} useType - 使用类型：'单次使用' 或 '多次使用'
     * @param {string} discountMode - 优惠模式：'立减' 或 '兑换' 等
     * @param {string} errorScenario - 错误场景测试类型，如 '单个用户使用限制次数需小于等于多次使用限制次数'
     */
    async CreateNewCoupons(useType = '单次使用', discountMode = '立减', errorScenario = null) {
        await basepage.openPage(baseUrl)
        codeConfig.couponCode = `UI自动化优惠码-${useType}-${discountMode}-${Date.now()}`
        await basepage.moveCursorAndClick(I, '新建')
        await basepage.moveCursorAndClick(I, '优惠码批次名称', codeConfig.couponCode)
        await basepage.moveCursorAndClick(I, '生成数量', "1")

        // 设置优惠码使用类型
        if (useType === '多次使用') {
            await basepage.moveCursorAndClick(I, '多次使用')
        }

        // 设置优惠模式
        if (discountMode === '兑换') {
            await basepage.moveCursorAndClick(I, '兑换')
        }

        // 设置有效期
        await basepage.editTime()

        // 选择申请人
        await this._selectApplicant('中台自动化')

        // 处理异常场景测试
        if (errorScenario === '单个用户使用限制次数需小于等于多次使用限制次数') {
            await this._testUserLimitExceedsMultipleLimit()
            return
        }

        // 正常保存
        await basepage.elementOperate("保存草稿").click()
        I.waitForText("新增成功", 20)
    },

    /**
     * 选择申请人
     * @param {string} applicantName - 申请人名称
     */
    async _selectApplicant(applicantName) {
        await basepage.elementOperate('//*[@placeholder="请选择申请人"]').click()
        await basepage.elementOperate('//*[@placeholder="搜索"]').fillField(applicantName)
        I.waitForText(applicantName, 20)
        I.wait(2)
        await basepage.elementOperate("//*[@class='simulate-checkbox']").click()
        await basepage.elementOperate("//*[@title='返回']").click()
        I.wait(2)
        await basepage.moveCursorAndClick(I, '确 定')
        I.waitForText(applicantName, 20)
    },

    /**
     * 测试用户使用限制次数超过多次使用限制的场景
     */
    async _testUserLimitExceedsMultipleLimit() {
        await basepage.moveCursorAndClick(I, "单个用户使用限制", "11")
        await basepage.elementOperate("保存草稿").click()
        I.waitForText("单个用户使用限制次数需小于等于多次使用限制次数！", 20)
    },

    //查询
    async SearchNewCoupons() {
        await basepage.openPage(baseUrl)
        await basepage.moveCursorAndClick(I, '优惠券名称', codeConfig.couponCode)
        await basepage.moveCursorAndClick(I, '查 询')
        I.waitForText(codeConfig.couponCode, 20)
        I.wait(2)
    },

    //编辑
    async EditNewCoupons() {
        await basepage.moveCursorAndClick(I, '编辑')
        await basepage.goToNewTab(I)
        I.wait(4)
        await basepage.elementOperate('保存草稿').click()
        I.waitForText("编辑成功", 20)

    },

    //产品列表
    async ProductListNewCoupons(scenarioType = '适用已选项目的全部CRM启用状态课程：否', errorScenario = null) {
        try {
            // 搜索新优惠券并打开产品列表标签页
            await this.SearchNewCoupons();
            await basepage.moveCursorAndClick(I, '产品列表');
            await basepage.goToNewTab(I);
            I.wait(4)

            if (scenarioType === "适用全部项目的CRM启用状态课程：是") {
                await this._applyToAllProjects();
            } else if (scenarioType === "适用已选项目的全部CRM启用状态课程：是") {
                if (errorScenario === '适用CRM项目范围为空') { //异常场景-适用CRM项目范围为空
                    await basepage.elementOperate("(//*[@class='ant-radio-input'])[3]").click()
                    await basepage.elementOperate('保 存').click();
                    I.waitForText('适用CRM项目范围不允许为空！', 20)
                    return
                }
                await this._applyToSelectedProjectsAll();
            } else {
                if (errorScenario === '指定产品为空') { //异常场景-指定产品为空
                    await this._selectProjectScope("阿米巴");
                    await basepage.elementOperate('保 存').click();
                    I.waitForText('指定产品不允许为空！', 20)
                    return
                } else if (errorScenario === '未绑定vid的课程/课程包') { //未绑定vid的课程/课程包
                    await this._selectProjectScope("阿米巴");
                    await basepage.moveCursorAndClick(I, "添加");
                    await basepage.moveCursorAndClick(I, "产品名称/ID", "UI自动化测试课程产品1749623645116");
                    await basepage.moveCursorAndClick(I, "查 询");
                    await basepage.elementOperate("(//*[@class='ant-checkbox-input'])[3]").click()
                    await basepage.moveCursorAndClick(I, "确 定");
                    I.waitForText('未绑定vid的课程/课程包不允许添加!', 20)
                    return
                }

                // 场景3: 适用已选项目的全部CRM启用状态课程：否
                await this._applyToSelectedProjectsSpecific();
            }
        } catch (error) {
            console.error(`产品列表关联失败: ${error.message}`);
            throw error;
        }
    },

    // 场景1: 适用全部项目的CRM启用状态课程
    async _applyToAllProjects() {
        // 选择"是"选项
        await basepage.elementOperate("//*[text()='是']").click()
        await this._saveAndWaitForSuccess();
    },

    // 场景2: 适用已选项目的全部CRM启用状态课程
    async _applyToSelectedProjectsAll() {
        // 选择适用已选项目选项
        await basepage.elementOperate("(//*[@class='ant-radio-input'])[3]").click()

        // 选择项目范围
        await this._selectProjectScope("阿米巴");

        // 保存并等待成功
        await this._saveAndWaitForSuccess();
    },

    // 场景3: 适用已选项目的指定课程
    async _applyToSelectedProjectsSpecific() {
        // 选择项目范围
        await this._selectProjectScope("阿米巴");

        // 添加具体产品
        await basepage.moveCursorAndClick(I, "添加");
        await basepage.moveCursorAndClick(I, "产品名称/ID", "M站阿米巴接口自动化广告");
        await basepage.moveCursorAndClick(I, "查 询");

        // 选择第三个复选框
        await basepage.elementOperate("(//*[@class='ant-checkbox-input'])[3]").click()
        await basepage.moveCursorAndClick(I, "确 定");

        // 保存并等待成功
        await this._saveAndWaitForSuccess();
    },

    // 选择项目范围
    async _selectProjectScope(projectName) {
        await basepage.moveCursorAndClick(I, "适用CRM项目范围：", projectName);
        I.pressKey("Enter");
        I.pressKey('Tab');
    },

    // 保存并等待成功提示
    async _saveAndWaitForSuccess() {
        await basepage.elementOperate('保 存').click();
        I.waitForText("关联成功!", 20);
    },

    //获取优惠码id
    async getBatchId() {
        // 1. 先获取token
        const token = await apiHelper.getAccessToken({
            loginUrl: '/api/v4/vigo/login',
            appid: '210666',
            GDSID: 'GE5JYVCI84Eaxpaby0z9rikyueckl9qdsrzd4abb441ej',
            user: '<EMAIL>',
            password: 'PwbwIeumyH19H9EWqLbuEVbZMFSP/Nn9rYvMQhRY+1wdLVHE+x4=',
        });
        //2.获取优惠券id
        const orderData = { "pageIndex": 1, "name": codeConfig.couponCode, "pageNum": 1, "pageSize": 10 }
        const response = await apiHelper.post(
            '/coupon/web/v2/discount-code-batch/list',  // 接口地址
            orderData,                // 请求数据
            {},
            false,                    // 是否表单数据
            token                     // 上面获取的 token
        );
        const data = response.data
        const batchId = data.result.list[0].id
        return batchId
    },

    //获取优惠码code
    async getCouponCode() {
        // 1. 先获取token
        const token = await apiHelper.getAccessToken({
            loginUrl: '/api/v4/vigo/login',
            appid: '210666',
            GDSID: 'GE5JYVCI84Eaxpaby0z9rikyueckl9qdsrzd4abb441ej',
            user: '<EMAIL>',
            password: 'PwbwIeumyH19H9EWqLbuEVbZMFSP/Nn9rYvMQhRY+1wdLVHE+x4=',
        });

        //获取优惠码id
        batchId = await this.getBatchId()

        //2.获取优惠码code
        const orderData = { "batchId": batchId, "pageIndex": 1, "pageSize": 10, "pageNum": 1 }
        const response = await apiHelper.post(
            '/coupon/web/v2/discount-code-batch/code/list',  // 接口地址
            orderData,                // 请求数据
            {},
            false,                    // 是否表单数据
            token                     // 上面获取的 token
        );
        const data = response.data
        const batchCode = data.result.list[0].couponCode
        return batchCode
    },

    //验证优惠码
    async verifyCode() {
        // 1. 先获取token
        const token = await apiHelper.getAccessToken({
            loginUrl: '/api/v1/ucenter/login',
            appid: '210804',
            GDSSID: 'VQO4AGYHWVBcqztwcc3z4m7gcmbgkk4105mk4irpfxglt',
            user: '12365870707',
            password: 'gaodun123@',
            PHPSESSIONID: 'iv7jecmobik91nj1ja5cs6r0u7',
        });
        batchCode = await this.getCouponCode()
        const orderData = { "couponCode": batchCode, "productNo": "CP-39054", "project": 1000053, "source": 130444, "userId": 978341544 }
        const response = await apiHelper.post(
            '/coupon/api/v2/discount-code/verify',  // 接口地址
            orderData,                // 请求数据
            {},
            false,                    // 是否表单数据
            token                     // 上面获取的 token
        );
    },

    //使用优惠码
    async createOrder() {
        // 1. 先获取token
        const token = await apiHelper.getAccessToken({
            loginUrl: '/api/v1/ucenter/login',
            appid: '210804',
            GDSSID: 'VQO4AGYHWVBcqztwcc3z4m7gcmbgkk4105mk4irpfxglt',
            user: '12365870707',
            password: 'gaodun123@',
            PHPSESSIONID: 'iv7jecmobik91nj1ja5cs6r0u7',
        });
        batchCode = await this.getCouponCode()
        const orderData = { "applyName": "niu07", "applyPhone": "13365870707", "applyPhoneCode": "86", "clueParameters": "", "orderSource": 3, "orderParentSource": 3, "orderPayabledPrice": 0, "projectList": [{ "projectId": 1000647, "productList": [{ "courseId": [41981], "discountType": 2, "discountCode": batchCode }] }] }
        const response = await apiHelper.post(
            '/gaodun-shopping/portal/v2/order/sys-course/create',  // 接口地址
            orderData,                // 请求数据
            {},
            false,                    // 是否表单数据
            token                     // 上面获取的 token
        );
    },

    //状态启用
    async EnableStatus() {
        await basepage.moveCursorAndClick(I, "修改状态")
        await basepage.moveCursorAndClick(I, "启用")
        await basepage.moveCursorAndClick(I, "确 定")
        I.waitForText("修改成功", 20)
    },

    //状态禁用
    async DisableStatus() {
        await basepage.moveCursorAndClick(I, "修改状态")
        await basepage.moveCursorAndClick(I, "停用")
        await basepage.moveCursorAndClick(I, "确 定")
        I.waitForText("修改成功", 20)
    },

    //删除
    async DeleteCoupon() {
        await basepage.moveCursorAndClick(I, "删除")
        I.waitForText("删除成功", 20)
    },
}