module.exports = function () {
  return actor({
    xpathId(locat) {
      return `.//*[@id="${locat}"]`
    },
    xpathId2(locat) {
      return `(.//*[@id="${locat}"])[2]`
    },
    xpathId3(locat) {
      return `//*[@title='${locat}']`
    },
    login(username, password, displayname) {
      this.wait(2)
      this.amOnPage("/login");
      this.waitForText('账号登录', 20);
      this.click(".qrcode-other");
      this.say("开始输入账号密码");
      this.fillField("$用户名", username);
      this.fillField("$密码", password);
      this.click("登 录");
      this.waitForText(displayname, 20)
    }
  });
}
