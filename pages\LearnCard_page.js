// 创建人：李季
// 创建日期：2025-01-09
// 创建人工号：138693
// 创建人邮箱：<EMAIL>
const { I, } = inject();
const { randomInt } = require('crypto');
const basepage = require('../tools/basepage');
const syncLearnCard = require('../tools/syncOrder_learnCard')
const apiHelper = require("../tools/apiHelper")

const baseUrl = '/system/goldengo/powerCenter/studyCard'
const cardConfig = {
    cardPlatform: 0,
    activateSCRMiniCourse: 0,
    projectQRCode: 0,
    learnCard: ''
};

module.exports = {
    cardConfig,

    //创建新的学习卡
    async CreateNewCard(platform = '内部') {
        try {
            await basepage.openPage(baseUrl)
            cardConfig.learnCard = "UI自动化学习卡组" + Date.now()

            await basepage.moveCursorAndClick(I, "+ 添加学习卡组")
            await basepage.moveCursorAndClick(I, "名称", cardConfig.learnCard)

            // 根据平台参数选择不同流程
            if (platform === '抖音') {
                cardConfig.cardPlatform = 1;
                await this.setupDouYinCard();
            } else {
                cardConfig.cardPlatform = 0;
                await this.setupInternalCard();
            }
        } catch (error) {
            console.error(`创建${platform}平台学习卡失败:`, error);
        }
    },

    // 设置抖音卡片
    async setupDouYinCard() {
        await basepage.moveCursorAndClick(I, "抖音")
        await this.selectCourse()
        await this.selectCategory()

        const shouldActivateSCRMiniCourse = this.getRandomBoolean(0.33);
        cardConfig.activateSCRMiniCourse = shouldActivateSCRMiniCourse ? 0 : 1;

        if (shouldActivateSCRMiniCourse) {
            await this.setupActivatedSCRMCourse();
        } else {
            await this.setupNonActivatedSCRMCourse();
        }
    },

    // 设置内部平台卡片
    async setupInternalCard() {
        await this.selectCourse()
        await basepage.moveCursorAndClick(I, "保 存")
    },

    // 设置激活SCRM小课的抖音卡片
    async setupActivatedSCRMCourse() {
        await basepage.moveCursorAndClick(I, "是")

        // 随机决定是否激活展示线索所属人二维码
        if (this.getRandomBoolean(0.33)) {
            await basepage.moveCursorAndClick(I, "是", null, null, null, null, 2)
        }

        await basepage.moveCursorAndClick(I, "保 存")
    },

    // 设置非激活SCRM小课的抖音卡片
    async setupNonActivatedSCRMCourse() {
        await basepage.moveCursorAndClick(I, "否")
        await this.uploadImage('//*[@type="file"]', 'ceshi.png')
        const QRCode = randomInt(1, 4)
        if (QRCode == 1) {
            await basepage.moveCursorAndClick(I, "线索所属人二维码")
        } else if (QRCode == 2) {
            cardConfig.projectQRCode = 1
            await basepage.moveCursorAndClick(I, "项目二维码")
            await this.uploadImage('(//*[@type="file"])[2]', 'ceshi.png')
        } else {
            await basepage.moveCursorAndClick(I, "服务学管")
        }


        await basepage.moveCursorAndClick(I, "保 存")
    },

    // 上传图片并等待完成
    async uploadImage(selector, filename) {
        await basepage.elementOperate(selector).upload(filename)
        I.waitForInvisible("文件上传中", 20)
    },

    // 选择课程的通用流程
    async selectCourse() {
        await basepage.moveCursorAndClick(I, "选择课程")
        await basepage.moveCursorAndClick(I, "CRM课程名称", "UI自动化测试课程权益中心勿动")
        await basepage.moveCursorAndClick(I, "查 询")
        I.wait(4)
        // await basepage.elementOperate("(//*[@class = 'ant-radio-input'])[11]").click()
        await basepage.moveCursorAndClick(I, "UI自动化测试课程权益中心勿动", null, -119)
        await basepage.moveCursorAndClick(I, "确 定")
    },

    // 选择分类
    async selectCategory() {
        await basepage.elementOperate("//*[@placeholder='请选择']").click()
        await basepage.elementOperate('//*[@placeholder="输入关键字搜索"]').fillField('UI自动化')
        await basepage.moveCursorAndClick(I, "UI自动化", null, -175, -12)
        await basepage.elementOperate("//*[@title='返回']").click()
        I.wait(2)
    },

    // 生成指定概率的随机布尔值
    getRandomBoolean(probability = 0.5) {
        return Math.random() < probability;
    },

    //搜索
    async SearchCard() {
        await basepage.openPage(baseUrl)
        await basepage.moveCursorAndClick(I, "卡组名称", cardConfig.learnCard)
        await basepage.moveCursorAndClick(I, "查 询")
        I.waitForText(cardConfig.learnCard, 20)
        I.wait(2)
    },

    //生成学习卡
    async GenerateCard() {
        await basepage.moveCursorAndClick(I, "生成学习卡")
        await basepage.moveCursorAndClick(I, "生成数量", "100")
        await basepage.moveCursorAndClick(I, "领取/激活截止时间", '2030-12-31 23:59:59')
        await basepage.moveCursorAndClick(I, "确 定", null, null, null, null, 2)
        await basepage.moveCursorAndClick(I, "确 定")
    },

    //编辑
    async EditCard() {
        await basepage.moveCursorAndClick(I, "编辑卡组")
        I.wait(2)

        if (cardConfig.activateSCRMiniCourse == 1) { //有编辑选项
            if (randomInt(1, 3) == 2) { //点击"项目二维码"选项

                await basepage.moveCursorAndClick(I, "项目二维码")
                if (cardConfig.projectQRCode == 1) {
                    await basepage.moveCursorAndClick(I, "保 存")
                    return
                } else {
                    await basepage.elementOperate('(//*[@type="file"])[2]').upload('ceshi.png')
                    I.waitForInvisible("文件上传中", 20)
                    I.wait(2)
                }

            } else { //点击"线索所属人二维码"选项
                await basepage.moveCursorAndClick(I, "线索所属人二维码")
            }
        }

        await basepage.moveCursorAndClick(I, "保 存")
    },

    //学习卡激活
    async activateCard() {
        const cardBatches = await this.GetCardBatches()
        await syncLearnCard.syncOrder_learnCard(cardBatches)
        await this.SearchCard()
        await basepage.moveCursorAndClick(I, "查看批次")
        await basepage.moveCursorAndClick(I, "查看")
    },

    //获取卡组id
    async getCardId() {
        // 1. 先获取token
        const token = await apiHelper.getAccessToken({
            loginUrl: '/api/v4/vigo/login',
            appid: '210666',
            GDSID: 'GE5JYVCI84Eaxpaby0z9rikyueckl9qdsrzd4abb441ej',
            user: '<EMAIL>',
            password: 'PwbwIeumyH19H9EWqLbuEVbZMFSP/Nn9rYvMQhRY+1wdLVHE+x4=',
        });
        //2.获取卡组id
        const orderData = {
            "name": cardConfig.learnCard,
            "startTime": "",
            "endTime": "",
            "pageNum": 1,
            "pageSize": 10,
            "pageIndex": 1
        };
        const response = await apiHelper.post(
            '/learn-card/crmweb/v1/learn-card-group/list',  // 接口地址
            orderData,                // 请求数据
            {},
            false,                    // 是否表单数据
            token                     // 上面获取的 token
        );
        const data = response.data
        const cardId = data.result.list[0].id
        return cardId
    },

    //获取卡批次编号
    async GetCardBatches() {
        // 1. 先获取token
        const token = await apiHelper.getAccessToken({
            loginUrl: '/api/v4/vigo/login',
            appid: '210666',
            GDSID: 'GE5JYVCI84Eaxpaby0z9rikyueckl9qdsrzd4abb441ej',
            user: '<EMAIL>',
            password: 'PwbwIeumyH19H9EWqLbuEVbZMFSP/Nn9rYvMQhRY+1wdLVHE+x4=',
        });
        //2.获取卡组id
        cardId = await this.getCardId()
        //3.获取卡批次编号
        const orderData = {
            "pageNum": 1,
            "pageSize": 10,
            "pageIndex": 1,
            "learnCardGroupId": cardId
        };
        const response = await apiHelper.post(
            '/learn-card/crmweb/v1/learn-card-batch/list',  // 接口地址
            orderData,                // 请求数据
            {},
            false,                    // 是否表单数据
            token                     // 上面获取的 token
        );
        const data = response.data
        const learnCardBatchNo = data.result.list[0].learnCardBatchNo
        return learnCardBatchNo
    },





}