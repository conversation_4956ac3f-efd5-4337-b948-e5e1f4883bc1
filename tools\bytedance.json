{"acceptOrderStatus": 1, "addressTagUi": [], "appId": 1128, "appointmentShipTime": 0, "authorCostAmount": 0, "awemeId": "", "biz": 2, "bizDesc": "小店", "buyerWords": "", "cancelReason": "", "channelPaymentNo": "*************************", "createTime": **********, "doudianOpenId": "1@#wM7tqFBWzisSoJth4HyKYMxVxXF5oXRPlzK2aTpxeXgIj7lVHpptHJPnrm8PQt8MO0Vx0lsp", "earliestReceiptTime": 0, "earlyArrival": false, "encryptPayTel": "***********", "encryptPostReceiver": "##R+9Y+R6va8WmNmpERgqMoruDIAAYrB7M1qUdqv/cPiPkC62WQ99V4e1bTAnmShjgbTKAsia3uuxa1WMd+d7BKCWgP1FDjImmjr5SPnecegk=*CgYIASAHKAESPgo8tuGCcgO9oj69zwJlPIqZ294/vGjAxwKW7sR1QgwjYpingVwR7CrhCh3B6DCQMknUjV4nLBtTO/ezSrXQGgA=#1##", "encryptPostTel": "***********", "expShipTime": **********, "finishTime": 0, "greetWords": "", "latestReceiptTime": 0, "logisticsInfo": [], "mainStatus": 2, "mainStatusDesc": "备货中", "maskPayTel": "", "maskPostAddr": {"detail": "", "street": {"id": "", "name": ""}}, "maskPostReceiver": "魏**", "maskPostTel": "1********65", "modifyAmount": 0, "modifyPostAmount": 0, "onlyPlatformCostAmount": 0, "openAddressId": "", "openId": "", "orderAmount": 1, "orderExpireTime": 1800, "orderId": "69305267504494078241428", "orderLevel": 2, "orderPhaseList": [], "orderRecycleInfo": {"appointRecycleEndTime": 0, "appointRecycleStartTime": 0, "quotedPrice": 0, "recycleMode": 0, "recycleStatus": 0}, "orderStatus": 2, "orderStatusDesc": "待发码", "orderType": 5, "orderTypeDesc": "商家发码订单", "packingAmount": 0, "payAmount": 1111, "payTel": "", "payTime": **********, "payType": 4, "platformCostAmount": 0, "postAddr": {"detail": "", "encryptDetail": "", "street": {"id": "", "name": ""}}, "postAmount": 0, "postInsuranceAmount": 0, "postOriginAmount": 0, "postPromotionAmount": 0, "postReceiver": "", "postTel": "", "promiseInfo": "{\"identifier\":\"S#2024-06-06\"}", "promotionAmount": 0, "promotionDetail": {"kolDiscountDetail": {"couponAmount": 0, "couponInfo": [], "fullDiscountAmount": 0, "fullDiscountInfo": [], "redpackAmount": 0, "redpackInfo": [], "totalAmount": 0}, "platformDiscountDetail": {"allowanceAmount": 0, "couponAmount": 0, "couponInfo": [], "fullDiscountAmount": 0, "fullDiscountInfo": [], "goldCoinAmount": 0, "officialDeductionAmount": 0, "redpackAmount": 0, "redpackInfo": [], "totalAmount": 0, "userBalanceAmount": 0}, "shopDiscountDetail": {"couponAmount": 0, "couponInfo": [], "fullDiscountAmount": 0, "fullDiscountInfo": [], "redpackAmount": 0, "redpackInfo": [], "totalAmount": 0}}, "promotionPayAmount": 0, "promotionPlatformAmount": 0, "promotionRedpackAmount": 0, "promotionRedpackPlatformAmount": 0, "promotionRedpackTalentAmount": 0, "promotionShopAmount": 0, "promotionTalentAmount": 0, "receiptDate": "", "receiptTimeMap": {}, "recommendEndShipTime": 0, "recommendStartShipTime": 0, "sellerRemarkStars": 0, "sellerWords": "", "serialNumberList": [], "shipTime": 0, "shopCostAmount": 0, "shopId": ********, "shopName": "高顿教育培训", "shopOrderTagUi": [], "skuOrderList": [{"accountList": {"accountInfo": []}, "adEnvType": "", "afterSaleInfo": {"afterSaleStatus": 0, "afterSaleType": 0, "refundStatus": 0}, "appId": 1128, "appointmentShipTime": 0, "authorCostAmount": 0, "authorId": 0, "authorName": "", "biz": 2, "bizDesc": "虚拟卡券", "bundleSkuInfo": [], "campaignInfo": [], "cancelReason": "", "cardVoucher": {"validDays": 0, "validEnd": **********, "validStart": **********}, "channelPaymentNo": "*************************", "cid": 0, "code": "aaaa24072402", "confirmReceiptTime": 0, "contentId": "0", "createTime": **********, "encryptPostReceiver": "##EMcppzrQCPyDtpOSG1jB+Fmtni7JMct5RubqthEA8O/ATsFS1CBZ+b8EANHhNH30uAKYrr4qS9H1C7zsFMUKzvV747YAfJ6DAW23z4ym4RI=*CgYIASAHKAESPgo8lYEFsU62+oGsuA/Ptjw/ZjPx96s31J6UBQGU9swg0qSZHLBToYBdkJQmLqN6O5B9zRuOKROosMb1vWFgGgA=#1##", "encryptPostTel": "***********", "expShipTime": **********, "finishTime": 0, "firstCid": 20015, "fourthCid": 0, "givenProductType": "", "goodsPrice": 1, "goodsType": 1, "hasTax": false, "inventoryList": [{"count": 1, "inventoryType": 1, "inventoryTypeDesc": "普通库存", "outWarehouseId": "", "warehouseId": "", "warehouseName": "", "warehouseType": 0}], "inventoryType": "", "inventoryTypeDesc": "", "isActivity": false, "isComment": 0, "itemNum": 1, "logisticsReceiptTime": 0, "mainStatus": 2, "mainStatusDesc": "备货中", "maskPostAddr": {"detail": "", "street": {"id": "", "name": ""}}, "maskPostReceiver": "魏**", "maskPostTel": "1********65", "masterSkuOrderId": "", "modifyAmount": 0, "modifyPostAmount": 0, "needSerialNumber": false, "onlyPlatformCostAmount": 0, "orderAmount": 1, "orderExpireTime": 1800, "orderId": "69305267504494078241428", "orderLevel": 3, "orderStatus": 2, "orderStatusDesc": "待发码", "orderType": 5, "orderTypeDesc": "商家发码订单", "originAmount": 1, "originId": "99514375927_3687350486043853035", "outProductId": "0", "outSkuId": "", "outWarehouseIds": [], "pageId": 0, "parentOrderId": "6930526750449407867", "payAmount": 1111, "payTime": **********, "payType": 4, "platformCostAmount": 0, "postAddr": {"detail": "", "encryptDetail": "", "street": {"id": "", "name": ""}}, "postAmount": 0, "postInsuranceAmount": 0, "postReceiver": "", "postTel": "", "preSaleType": 0, "productId": 6927190866538468983, "productIdStr": "6927190866538468983", "productName": "田_小课测试商品2", "productPic": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/ZSvkoTBr_m_16ccc19b10a1f06a504e253f04b82ee5_sx_178712_www800-800", "promiseInfo": "{\"identifier\":\"S#2024-06-06\"}", "promotionAmount": 0, "promotionDetail": {"kolDiscountDetail": {"couponAmount": 0, "couponInfo": [], "fullDiscountAmount": 0, "fullDiscountInfo": [], "redpackAmount": 0, "redpackInfo": [], "totalAmount": 0}, "platformDiscountDetail": {"allowanceAmount": 0, "couponAmount": 0, "couponInfo": [], "fullDiscountAmount": 0, "fullDiscountInfo": [], "goldCoinAmount": 0, "officialDeductionAmount": 0, "redpackAmount": 0, "redpackInfo": [], "totalAmount": 0, "userBalanceAmount": 0}, "shopDiscountDetail": {"couponAmount": 0, "couponInfo": [], "fullDiscountAmount": 0, "fullDiscountInfo": [], "redpackAmount": 0, "redpackInfo": [], "totalAmount": 0}}, "promotionPayAmount": 0, "promotionPayAmountDetails": [], "promotionPlatformAmount": 0, "promotionRedpackAmount": 0, "promotionRedpackPlatformAmount": 0, "promotionRedpackTalentAmount": 0, "promotionShopAmount": 0, "promotionTalentAmount": 0, "qualityInspectionStatus": 0, "receiveType": 0, "reduceStockType": 3, "reduceStockTypeDesc": "支付减库存预占", "relationOrder": {}, "roomId": 0, "roomIdStr": "0", "secondCid": 20342, "sendPay": 0, "sendPayDesc": "-", "shipTime": 0, "shopCostAmount": 0, "skuCarModelDesc": "", "skuCustomizationInfo": [], "skuId": 2407241426111, "skuOrderTagUi": [{"extra": "", "helpDoc": "", "hoverText": "", "key": "refund_anytime", "sort": 0, "tagType": "", "text": "随时退"}, {"extra": "", "helpDoc": "", "hoverText": "", "key": "auto_refund", "sort": 0, "tagType": "", "text": "过期退"}, {"extra": "", "helpDoc": "", "hoverText": "不支持7天无理由", "key": "unsupport_7days_refund", "sort": 0, "tagType": "grey", "text": "不支持7天"}, {"extra": "", "helpDoc": "", "hoverText": "达人通过「我的店铺」、或通过链接添加人店一体店铺商品（无官方店铺情况下，添加渠道号店铺商品也算）到橱窗、视频、直播，产生的订单为自卖订单", "key": "c_biz_self_sell", "sort": 0, "tagType": "grey", "text": "小店自卖"}, {"extra": "{\"ProductId\":0,\"SkuId\":0,\"ShopId\":0,\"StoreId\":0}", "helpDoc": "", "hoverText": "", "key": "sku_original_product_info", "sort": 0, "tagType": "", "text": ""}], "sourcePlatform": "", "spec": [{"name": "默认", "value": "默认"}], "subBType": 3, "subBTypeDesc": "H5", "sumAmount": 1, "supplierId": "", "taxAmount": 0, "taxAmountNotComeOut": 0, "themeType": "0", "themeTypeDesc": "-", "thirdCid": 23252, "tradeType": 0, "tradeTypeDesc": "普通", "updateTime": **********, "videoId": "", "warehouseIds": [], "writeoffInfo": []}], "subBType": 3, "subBTypeDesc": "H5", "supermarketOrderSerialNo": "", "targetArrivalTime": 0, "taxAmount": 0, "taxAmountNotComeOut": 0, "totalPromotionAmount": 0, "tradeType": 0, "tradeTypeDesc": "普通", "updateTime": **********, "userIcon": "", "userNickName": "", "userTagUi": [{"key": "user_profile_buy_frequency", "text": "高频购买"}, {"key": "user_profile_shop_customer_type", "text": "店铺新客"}]}