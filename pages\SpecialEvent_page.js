// 创建人：李季
// 创建日期：2025-01-08
// 创建人工号：138693
// 创建人邮箱：<EMAIL>
const { I, } = inject();
const { randomInt } = require('crypto');
const basepage = require('../tools/basepage')

const baseUrl = '/system/goldengo/powerCenter/special'
const eventConfig = {
    eventName: ''
}

module.exports = {
    eventConfig,

    //创建专题活动
    async CreatEvent(topicModule = "顶部广告") {
        try {
            // 初始化活动基本信息
            await this.initEventBasicInfo();
            
            // 选择项目
            await basepage.moveCursorAndClick(I, "选择项目", "阿米巴");
            I.pressKey("Enter");

            // 根据模块类型处理不同的模块添加逻辑
            switch (topicModule) {
                case "顶部广告":
                    await this.addTopAdModule();
                    break;
                case "锚点TAB":
                    await this.addAnchorTabModule();
                    break;
                case "合作商品":
                    await this.addCoopGoodsModule();
                    break;
                case "图片":
                    await this.addPictureModule();
                    break;
                default:
                    throw new Error(`不支持的模块类型: ${topicModule}`);
            }
        } catch (error) {
            console.error(`创建专题活动失败: ${error.message}`);
            await I.saveScreenshot(`error_${Date.now()}.png`);
            throw error;
        }
    },

    // 初始化活动基本信息
    async initEventBasicInfo() {
        await basepage.openPage(baseUrl);
        eventConfig.eventName = "UI自动化专题" + Date.now();
        await basepage.moveCursorAndClick(I, "创建专题");
        await basepage.moveCursorAndClick(I, "活动名称", eventConfig.eventName);
        await basepage.moveCursorAndClick(I, "活动简介", eventConfig.eventName);
        await basepage.editTime2();
        await basepage.moveCursorAndClick(I, "选 择");
        await basepage.elementOperate("//*[@class = 'ant-checkbox-input']").click();
        await basepage.moveCursorAndClick(I, "确 定", null, null, null, null, 2);

        // 随机选择是否点击合作方账号
        if (randomInt(1, 3) == 2) {
            await basepage.moveCursorAndClick(I, "合作方账号");
            await basepage.moveCursorAndClick(I, "合作方名称", eventConfig.eventName);
        }

        await basepage.moveCursorAndClick(I, "保存并下一步");
    },

    // 保存并发布
    async saveAndPublish() {
        await basepage.moveCursorAndClick(I, "保存并预览");
        await basepage.moveCursorAndClick(I, "发 布");
        await I.waitForText("保存成功", 20);
    },

    // 上传文件
    async uploadFile(selector = '//*[@type="file"]', fileName = 'ceshi.png') {
        await basepage.elementOperate(selector).upload(fileName);
        await I.wait(2); // 等待上传完成
    },

    // 添加顶部广告模块
    async addTopAdModule() {
        await basepage.mouseDrag(I, "顶部广告", 500);
        await basepage.moveCursorAndClick(I, "+添加");
        await this.uploadFile();
        await this.saveAndPublish();
    },

    // 添加锚点TAB模块
    async addAnchorTabModule() {
        // 尝试添加锚点TAB，预期会有提示
        await basepage.mouseDrag(I, "锚点TAB", 500);
        await I.waitForText("预览区域模块至少一个", 20);
        
        // 先添加顶部广告
        await basepage.mouseDrag(I, "顶部广告", 500);
        await basepage.moveCursorAndClick(I, "+添加");
        
        // 再次尝试添加锚点TAB，预期会有提示
        await basepage.mouseDrag(I, "锚点TAB", 500);
        await I.waitForText("只有顶部广告时不能添加锚点TAB", 20);
        
        // 完成顶部广告添加
        await basepage.moveCursorAndClick(I, "+添加");
        await this.uploadFile();
        await basepage.moveCursorAndClick(I, "保存并预览");
        await I.wait(2);
        
        // 添加图片模块
        await basepage.mouseDrag(I, "图片", 500);
        await basepage.moveCursorAndClick(I, "+添加");
        await this.uploadFile();
        await basepage.moveCursorAndClick(I, "添加图片");
        await this.uploadFile('(//*[@type="file"])[2]');
        await basepage.moveCursorAndClick(I, "完 成");
        
        // 添加锚点TAB并发布
        await basepage.moveCursorAndClick(I, "保存并预览");
        await basepage.mouseDrag(I, "锚点TAB", 500);
        await basepage.moveCursorAndClick(I, "发 布");
        await I.waitForText("保存成功", 20);
    },

    // 添加合作商品模块
    async addCoopGoodsModule() {
        await basepage.mouseDrag(I, "合作商品", 500);
        await basepage.moveCursorAndClick(I, "+添加");
        await basepage.moveCursorAndClick(I, "标题名称", eventConfig.eventName);
        await basepage.moveCursorAndClick(I, "添加商品");
        await basepage.moveCursorAndClick(I, "商品名称", eventConfig.eventName);
        await basepage.moveCursorAndClick(I, "跳转地址", "https://www.baidu.com");
        await this.uploadFile();
        await basepage.moveCursorAndClick(I, "完 成");
        await this.saveAndPublish();
    },

    // 添加图片模块
    async addPictureModule() {
        await basepage.mouseDrag(I, "图片", 500);
        await basepage.moveCursorAndClick(I, "+添加");
        await this.uploadFile();
        await basepage.moveCursorAndClick(I, "添加图片");
        await this.uploadFile('(//*[@type="file"])[2]');
        await basepage.moveCursorAndClick(I, "完 成");
        await this.saveAndPublish();
    },

    //查询
    async SearchEvent() {
        await basepage.openPage(baseUrl)
        await basepage.moveCursorAndClick(I, "活动名称", eventConfig.eventName)
        await basepage.moveCursorAndClick(I, "查 询")
        I.wait(2)
        I.waitForText(eventConfig.eventName, 20)
    },

    //编辑
    async EditEvent() {
        await basepage.moveCursorAndClick(I, "编辑")
        await basepage.moveCursorAndClick(I, "保存并下一步")
        I.waitForText("保存成功!", 20)
        I.wait(2)
        await basepage.moveCursorAndClick(I, "发 布")
        I.waitForText('保存成功!', 20)
    },

    //投放渠道
    async DeliveryChannel() {
        await basepage.moveCursorAndClick(I, "投放渠道")
        await basepage.moveCursorAndClick(I, "新增渠道")
        I.wait(2)
        await basepage.moveCursorAndClick(I, "请选择渠道", "UI自动化")
        await basepage.moveCursorAndClick(I, "UI自动化")
        await basepage.moveCursorAndClick(I, "请输入姓名", "李季")
        I.pressKey('Enter')
        I.pressKey('Tab')
        await basepage.moveCursorAndClick(I, '确 定')
        I.waitForText('新增渠道成功!', 20)
        await basepage.moveCursorAndClick(I, '复制链接')
        I.waitForText('复制成功！', 20)
    },

    //状态启用
    async EnableStatus() {
        await basepage.moveCursorAndClick(I, "停用")
        I.waitForText('启用', 20)
    },

    //状态停用
    async DisableStatus() {
        await basepage.moveCursorAndClick(I, "启用")
        I.waitForText('停用', 20)
    },





}