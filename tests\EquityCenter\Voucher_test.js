// 创建人：李季
// 创建日期：2025-01-03
// 创建人工号：138693
// 创建人邮箱：<EMAIL>
const basepage = require("../../tools/basepage")
const voucher = require("../../pages/Voucher_page")
const apiHelper = require("../../tools/apiHelper")
const voucherParameters = {
  discountCode: "",
  discountType: ""
}

Feature('权益中心-优惠券');

Before(async ({ login }) => {
  await login('Testzhongtai');
});


Scenario('优惠券-创建-折扣', async ({ I }) => {
  await voucher.CreateNewCoupons()
});

Scenario('优惠券-创建-满减', async ({ I }) => {
  await voucher.CreateNewCoupons("满减")
});

Scenario('优惠券-创建-立减', async ({ I }) => {
  await voucher.CreateNewCoupons("立减")
});

Scenario('优惠券-创建-兑换', async ({ I }) => {
  await voucher.CreateNewCoupons("兑换")
});

Scenario('优惠券-创建-联保立减', async ({ I }) => {
  await voucher.CreateNewCoupons("联保立减")
});

Scenario('优惠券-查询', async ({ I }) => {
  await voucher.SearchNewCoupons()
});

Scenario('优惠券-编辑', async ({ I }) => {
  await voucher.SearchNewCoupons()
  await voucher.EditNewCoupons()
});

Scenario('优惠券-详情', async ({ I }) => {
  await voucher.SearchNewCoupons()
  await basepage.moveCursorAndClick(I, "详情")
  I.waitForText("创建人", 20)
});

Scenario('优惠券-产品列表', async ({ I }) => {
  await voucher.ProductListNewCoupons()
});

Scenario('优惠券-渠道配置', async ({ I }) => {
  await voucher.SearchNewCoupons()
  await basepage.moveCursorAndClick(I, "渠道配置")
  await basepage.moveCursorAndClick(I, "全部")
  await basepage.moveCursorAndClick(I, "保 存")
  I.waitForText("保存成功", 20)
});

Scenario('优惠券-官网链接', async ({ I }) => {
  await voucher.SearchNewCoupons()
  await basepage.moveCursorAndClick(I, "官网链接")
  await basepage.moveCursorAndClick(I, "复制链接")
  I.waitForText("复制成功！", 20)
  await basepage.moveCursorAndClick(I, "确 定")
});

Scenario('优惠券-修改状态-启用', async ({ I }) => {
  await voucher.SearchNewCoupons()
  await voucher.EnableStatus()
});

Scenario('优惠券-使用统计-领取优惠券', async ({ I }) => {
  const { voucherId, voucherType } = await voucher.SearchNewCoupons();
  // 1. 先获取token
  const token = await apiHelper.getAccessToken({
    loginUrl: '/api/v1/ucenter/login',
    appid: '210804',
    GDSSID: '1E22LXGFJR6z99va75ijocm14cu1a99u3rwme47bqreyp',
    user: '12365870707',
    password: 'gaodun123@',
    PHPSESSIONID: '10l7masn20el0mpk6rrhnilpq5',
  });

  const orderData = { "discountId": voucherId, "produceSource": 130444 }
  const response = await apiHelper.post(
    '/coupon/api/v2/coupon/receive',  // 接口地址
    orderData,                // 请求数据
    {},
    false,                    // 是否表单数据
    token                     // 上面获取的 token
  );
  voucherParameters.discountCode = response.data.result.code
  voucherParameters.discountType = response.data.result.validationType
  await basepage.moveCursorAndClick(I, "使用统计")
  I.wait(4)
  // 直接验证页面上的文本
  I.see("1", "(//*[@class='ant-statistic-content-value-int'])[2]");
  I.see(voucherParameters.discountCode, "(//*[@class= 'ant-table-cell'])[12]")
});

Scenario('优惠券-使用统计-使用优惠券', async ({ I }) => {
  const { voucherId, voucherType } = await voucher.SearchNewCoupons();
  // 1. 先获取token
  const token = await apiHelper.getAccessToken({
    loginUrl: '/api/v1/ucenter/login',
    appid: '210804',
    GDSSID: '1E22LXGFJR6z99va75ijocm14cu1a99u3rwme47bqreyp',
    user: '12365870707',
    password: 'gaodun123@',
    PHPSESSIONID: '10l7masn20el0mpk6rrhnilpq5',
  });
  const orderData = {
    "applyName": "niu07",
    "applyPhone": "13365870707",
    "applyPhoneCode": "86",
    "clueParameters": "",
    "orderSource": 3,
    "orderParentSource": 3,
    "orderPayabledPrice": 9900,
    "projectList": [
      {
        "projectId": 1000053,
        "productList": [
          {
            "courseId": [
              71952
            ],
            "discountType": voucherType,
            "discountCode": voucherParameters.discountCode,

          }
        ]
      }
    ]
  }
  await apiHelper.post(
    '/gaodun-shopping/portal/v2/order/sys-course/create',  // 接口地址
    orderData,                // 请求数据
    {},
    false,                    // 是否表单数据
    token                     // 上面获取的 token
  );
  await basepage.moveCursorAndClick(I, "使用统计")
  I.wait(4)
  // 直接验证页面上的文本
  I.see("1", "(//*[@class='ant-statistic-content-value-int'])[4]");
});

Scenario('优惠券-修改状态-停用', async ({ I }) => {
  await voucher.SearchNewCoupons()
  await voucher.DisableStatus()
});

Scenario('优惠券-删除', async ({ I }) => {
  await voucher.SearchNewCoupons()
  await voucher.DeleteCoupon()
});

Scenario('优惠券-批量删除', async ({ I }) => {
  await voucher.CreateNewCoupons()
  await voucher.ProductListNewCoupons()
  await voucher.SearchNewCoupons()
  await voucher.EnableStatus()
  await voucher.DisableStatus()
  await basepage.elementOperate("//*[@class='ant-checkbox-input']").click()
  await basepage.moveCursorAndClick(I, "批量删除")
  await basepage.elementOperate("确 定").click()
  I.waitForText("批量删除成功", 20)
});


