// 创建人：李季
// 创建日期：2025-01-09
// 创建人工号：138693
// 创建人邮箱：<EMAIL>
const card = require("../../pages/LearnCard_page")
const basepage = require("../../tools/basepage")


Feature('权益中心-学习卡管理');

Before(async ({ login }) => {
    await login('Testzhongtai');
});

Scenario('学习卡管理-添加内部学习卡组', async ({ I }) => {
    await card.CreateNewCard("内部")
    I.waitForText("新增成功", 20)
});

// 创建日期：2025-07-04
Scenario('学习卡管理-添加抖音学习卡组', async ({ I }) => {
    await card.CreateNewCard("抖音")
    I.waitForText("新增成功", 20)
});

Scenario('学习卡管理-查询学习卡', async ({ I }) => {
    await card.SearchCard()
});

Scenario('学习卡管理-生成学习卡', async ({ I }) => {
    await card.SearchCard()
    await card.GenerateCard()
    I.waitForText("生成成功", 20)
});

Scenario('学习卡管理-编辑', async ({ I }) => {
    //非抖音card不允许编辑，直接return
    if (card.cardConfig.cardPlatform == 0) {
        return
    }
    await card.SearchCard()
    await card.EditCard()
    I.waitForText('编辑成功', 20)
});

Scenario('学习卡管理-查看卡组', async ({ I }) => {
    await card.SearchCard()
    await basepage.moveCursorAndClick(I, "查看卡组")
    I.waitForText("是否激活SCRM小课", 20)
});

Scenario('学习卡管理-操作日志', async ({ I }) => {
    await card.SearchCard()
    await basepage.moveCursorAndClick(I, "操作日志")
    I.waitForText("事件类型", 20)
});

Scenario('学习卡管理-查看批次', async ({ I }) => {
    await card.SearchCard()
    await basepage.moveCursorAndClick(I, "查看批次")
    I.waitForText("学习卡批次列表", 20)
});

Scenario('学习卡管理-抖音学习卡激活', async ({ I }) => {
    //非抖音card没有激活链路，直接return
    if (card.cardConfig.cardPlatform == 0) {
        return
    }
    await card.activateCard()
    I.waitForText("已激活", 20)
});

Scenario('学习卡管理-批次-状态停用', async ({ I }) => {
    await card.SearchCard()
    await basepage.moveCursorAndClick(I, "查看批次")
    await basepage.moveCursorAndClick(I, "启用")
    I.waitForText("停用成功", 20)
});

Scenario('学习卡管理-批次-状态启用', async ({ I }) => {
    await card.SearchCard()
    await basepage.moveCursorAndClick(I, "查看批次")
    await basepage.moveCursorAndClick(I, "停用")
    I.waitForText("启用成功", 20)
});

Scenario('学习卡管理-查看学习卡', async ({ I }) => {
    await card.SearchCard()
    await basepage.moveCursorAndClick(I, "查看批次")
    await basepage.moveCursorAndClick(I, "查看")
    I.waitForText("卡批次编号", 20)
});

Scenario('学习卡管理-学习卡-停用学习卡', async ({ I }) => {
    await card.SearchCard()
    await basepage.moveCursorAndClick(I, "查看批次")
    await basepage.moveCursorAndClick(I, "查看")
    await basepage.moveCursorAndClick(I, "启用", null, null, null, null, 2)
    I.waitForText("停用成功", 20)
});

Scenario('学习卡管理-学习卡-启用学习卡', async ({ I }) => {
    await card.SearchCard()
    await basepage.moveCursorAndClick(I, "查看批次")
    await basepage.moveCursorAndClick(I, "查看")
    await basepage.moveCursorAndClick(I, "停用")
    I.waitForText("启用成功", 20)
});

// 创建日期：2025-07-04
Scenario('学习卡管理-学习卡-批量停用学习卡-存在激活的学习卡', async ({ I }) => {
    //非抖音card没有激活链路，直接return
    if (card.cardConfig.cardPlatform == 0) {
        return
    }
    await card.SearchCard()
    await basepage.moveCursorAndClick(I, "查看批次")
    await basepage.moveCursorAndClick(I, "查看")
    await basepage.elementOperate('(//*[@class="ant-checkbox-input"])').click()
    await basepage.moveCursorAndClick(I, "批量停用")
    await basepage.moveCursorAndClick(I, "确 定")
    I.waitForText("存在已激活的学习卡，无法批量停用", 20)
});

Scenario('学习卡管理-学习卡-批量停用学习卡-不存在激活的学习卡', async ({ I }) => {
    await card.SearchCard()
    await basepage.moveCursorAndClick(I, "查看批次")
    await basepage.moveCursorAndClick(I, "查看")
    await basepage.elementOperate('(//*[@class="ant-checkbox-input"])[9]').click()
    await basepage.elementOperate('(//*[@class="ant-checkbox-input"])[10]').click()
    await basepage.moveCursorAndClick(I, "批量停用")
    await basepage.moveCursorAndClick(I, "确 定")
    I.waitForText("批量停用成功", 20)
});

Scenario('学习卡管理-学习卡-批量启用学习卡', async ({ I }) => {
    await card.SearchCard()
    await basepage.moveCursorAndClick(I, "查看批次")
    await basepage.moveCursorAndClick(I, "查看")
    await basepage.elementOperate('(//*[@class="ant-checkbox-input"])').click()
    await basepage.moveCursorAndClick(I, "批量启用")
    await basepage.moveCursorAndClick(I, "确 定")
    I.waitForText("批量启用成功", 20)
});