// 创建人：李季
// 创建日期：2025-01-08
// 创建人工号：138693
// 创建人邮箱：<EMAIL>
const coupon = require("../../pages/CouponCode_page")
const basepage = require("../../tools/basepage")
const mysql = require('mysql'); 

Feature('权益中心-优惠码');

Before(async ({ login }) => {
    await login('Testzhongtai');
});

// 创建日期：2025-07-04
Scenario('优惠码-新建-单次使用-兑换', async ({ I }) => {
    await coupon.CreateNewCoupons("单次使用", "兑换")
});

// 创建日期：2025-07-04
Scenario('优惠码-新建-多次使用-立减', async ({ I }) => {
    await coupon.CreateNewCoupons("多次使用", "立减")
});

// 创建日期：2025-07-04
Scenario('优惠码-新建-多次使用-兑换', async ({ I }) => {
    await coupon.CreateNewCoupons("多次使用", "兑换")
});

// 创建日期：2025-07-04
Scenario('优惠码-新建-单次使用-单个用户使用限制次数需小于等于多次使用限制次数', async ({ I }) => {
    await coupon.CreateNewCoupons("单次使用", "立减", "单个用户使用限制次数需小于等于多次使用限制次数")
});

// 创建日期：2025-07-04
Scenario('优惠码-新建-多次使用-单个用户使用限制次数需小于等于多次使用限制次数', async ({ I }) => {
    await coupon.CreateNewCoupons("多次使用", "立减", "单个用户使用限制次数需小于等于多次使用限制次数")
});

Scenario('优惠码-新建-单次使用-立减', async ({ I }) => {
    await coupon.CreateNewCoupons()
});

Scenario('优惠码-查询', async ({ I }) => {
    await coupon.SearchNewCoupons()
});

Scenario('优惠码-编辑', async ({ I }) => {
    await coupon.SearchNewCoupons()
    await coupon.EditNewCoupons()
});

Scenario('优惠码-详情', async ({ I }) => {
    await coupon.SearchNewCoupons()
    await basepage.moveCursorAndClick(I, "详情")
    await basepage.goToNewTab(I)
    I.wait(4)
    I.waitForText("优惠码批次名称", 20)
});

// 创建日期：2025-07-04
Scenario('优惠码-产品列表-适用CRM项目范围为空！', async ({ I }) => {
    await coupon.ProductListNewCoupons("适用已选项目的全部CRM启用状态课程：是", "适用CRM项目范围为空")
});

// 创建日期：2025-07-04
Scenario('优惠码-产品列表-指定产品为空', async ({ I }) => {
    await coupon.ProductListNewCoupons("适用已选项目的全部CRM启用状态课程：否", "指定产品为空")
});

// 创建日期：2025-07-04
Scenario('优惠码-产品列表-未绑定vid的课程/课程包', async ({ I }) => {
    await coupon.ProductListNewCoupons("适用已选项目的全部CRM启用状态课程：否", "未绑定vid的课程/课程包")
});

// 创建日期：2025-07-04
Scenario('优惠码-产品列表-适用已选项目的全部CRM启用状态课程：是', async ({ I }) => {
    await coupon.CreateNewCoupons()
    await coupon.ProductListNewCoupons("适用已选项目的全部CRM启用状态课程：是")
});

// 创建日期：2025-07-04
Scenario('优惠码-产品列表-适用已选项目的全部CRM启用状态课程：否', async ({ I }) => {
    await coupon.CreateNewCoupons()
    await coupon.ProductListNewCoupons("适用已选项目的全部CRM启用状态课程：否")
});

// 创建日期：2025-07-04
Scenario('优惠码-产品列表-适用全部项目的CRM启用状态课程：是', async ({ I }) => {
    await coupon.CreateNewCoupons("单次使用", "兑换")
    await coupon.ProductListNewCoupons("适用全部项目的CRM启用状态课程：是")
});

Scenario('优惠码-渠道配置', async ({ I }) => {
    await coupon.SearchNewCoupons()
    await basepage.moveCursorAndClick(I, "渠道配置")
    await basepage.goToNewTab(I)
    I.wait(4)
    await basepage.moveCursorAndClick(I, "保 存")
    I.waitForText("保存成功", 20)
});

Scenario('优惠码-修改状态-启用', async ({ I }) => {
    await coupon.SearchNewCoupons()
    await coupon.EnableStatus()
});

Scenario('优惠码-使用统计-使用优惠码', async ({ I }) => {
    await coupon.createOrder()
    await coupon.SearchNewCoupons()
    await basepage.moveCursorAndClick(I, "使用统计")
    await basepage.goToNewTab(I)
    I.waitForText('978341544', 20)
});

Scenario('优惠码-修改状态-停用', async ({ I }) => {
    await coupon.SearchNewCoupons()
    await coupon.DisableStatus()
});

Scenario('优惠码-删除', async ({ I }) => {
    await coupon.SearchNewCoupons()
    await coupon.DeleteCoupon()
});


// 整个测试文件执行完后清理数据
AfterSuite(async () => {
    // 数据库连接参数
    const conn_params = {
        'user': 'liji',
        'password': 'buflxn3tvqsoh0jg_pamciPLz1rywe',
        'host': 't.dede_master.mysql.gaodunwangxiao.com',
        'port': 3306
    };
    
    // 创建数据库连接
    const connection = mysql.createConnection(conn_params);
    
    try {
        // 连接数据库
        connection.connect((err) => {
            if (err) {
                console.error('数据库连接失败:', err);
                return;
            }
            console.log('数据库连接成功');
            
            // 执行删除语句
            connection.query('DELETE from gd_shoping.order_info where user_id = ?', ['978341544'], (err, result) => {
                if (err) {
                    console.error('删除操作失败:', err);
                    return;
                }
                console.log(`成功删除${result.affectedRows}条记录`);
                
                // 关闭连接
                connection.end((err) => {
                    if (err) {
                        console.error('关闭连接失败:', err);
                        return;
                    }
                    console.log('数据库连接已关闭');
                });
            });
        });
    } catch (error) {
        console.error('数据库操作失败:', error.message);
        // 确保连接关闭
        if (connection) {
            connection.end();
        }
    }
});
