const axios = require('axios');
const qs = require('qs');

// API配置 - 默认配置，可以被覆盖
const API_CONFIG = {
    BASE_URL: 'https://t-apigateway.gaodun.com',
    LOGIN_URL: '/api/v4/vigo/login'
};

/**
 * 获取accessToken 
 * @param {Object} loginConfig - 登录配置，可以包含任意登录参数
 * @param {string} loginConfig.baseUrl - API基础URL，默认为API_CONFIG.BASE_URL
 * @param {string} loginConfig.loginUrl - 登录接口路径
 * @returns {Promise<string>} accessToken
 */
async function getAccessToken(loginConfig = {}) {
    try {
        // 提取基础URL和登录URL
        const baseUrl = loginConfig.baseUrl || API_CONFIG.BASE_URL;
        const loginUrl = loginConfig.loginUrl || API_CONFIG.LOGIN_URL;
        
        const loginParams = { ...loginConfig };
        delete loginParams.baseUrl;
        delete loginParams.loginUrl;
        
        console.log(`开始获取accessToken，用户: ${loginParams.user || '未指定'}...`);
        console.log(`请求URL: ${baseUrl}${loginUrl}`);
        console.log(`请求参数: ${JSON.stringify(loginParams)}`);
        
        const response = await axios.post(
            `${baseUrl}${loginUrl}`,
            qs.stringify(loginParams),
            {
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            }
        );

        console.log('响应状态码:', response.status);
        
        // 尝试从多个可能的位置获取token
        let token = findTokenInResponse(response);
        
        if (token) {
            console.log(`获取accessToken成功`);
            return token;
        } else {
            // 输出所有可能包含token的数据
            console.log('响应头:', JSON.stringify(response.headers, null, 2));
            if (response.data) {
                console.log('响应数据:', JSON.stringify(response.data, null, 2));
            }
            throw new Error('无法找到accessToken：响应中没有包含token数据');
        }
        
    } catch (error) {
        console.error('获取accessToken失败:', error.message);
        if (error.response) {
            console.error('响应状态:', error.response.status);
            if (error.response.data) {
                console.error('错误详情:', JSON.stringify(error.response.data, null, 2));
            }
        }
        throw error;
    }
}

/**
 * 从响应中查找token
 * @param {Object} response - Axios响应对象
 * @returns {string|null} 找到的token或null
 */
function findTokenInResponse(response) {
    // 1. 检查响应头 (不区分大小写)
    const headers = response.headers || {};
    const headerNames = Object.keys(headers).map(key => key.toLowerCase());
    
    // 常见的token头名称
    const tokenHeaderNames = ['accesstoken', 'access-token', 'authorization', 'authentication', 'x-auth-token', 'token', 'refreshtoken'];
    
    // 在响应头中查找token
    for (const name of tokenHeaderNames) {
        for (const headerName of headerNames) {
            if (headerName.toLowerCase() === name) {
                return headers[headerName];
            }
        }
    }
    
    // 2. 如果响应头中没有，检查响应体
    const data = response.data || {};
    
    // 常见的token字段路径
    const tokenPaths = [
        'accessToken',
        'access_token',
        'token',
        'auth_token',
        'data.accessToken',
        'data.token',
        'result.accessToken',
        'result.token'
    ];
    
    // 在响应体中按路径查找token
    for (const path of tokenPaths) {
        const token = getNestedValue(data, path);
        if (token) {
            return token;
        }
    }
    
    return null;
}

/**
 * 根据路径获取嵌套对象中的值
 * @param {Object} obj - 对象
 * @param {string} path - 路径，如 'data.user.name'
 * @returns {any} 找到的值或undefined
 */
function getNestedValue(obj, path) {
    return path.split('.').reduce((prev, curr) => {
        return prev && prev[curr] !== undefined ? prev[curr] : undefined;
    }, obj);
}

/**
 * 通用接口调用方法
 * @param {Object} options - 请求配置
 * @param {string} options.url - 接口地址
 * @param {string} options.method - 请求方法 (GET/POST)
 * @param {Object} options.data - 请求数据
 * @param {Object} options.params - URL参数
 * @param {Object} options.headers - 请求头
 * @param {boolean} options.isFormData - 是否使用表单格式
 * @param {string} options.token - 认证令牌，必须由调用者提供
 * @param {string} options.baseUrl - API基础URL，默认为API_CONFIG.BASE_URL
 * @returns {Promise<Object>} 响应数据
 */
async function callApi(options = {}) {
    const {
        url,
        method = 'GET',
        data = {},
        params = {},
        headers = {},
        isFormData = false,
        token,
        baseUrl = API_CONFIG.BASE_URL
    } = options;

    try {
        // 验证token是否提供
        // if (!token) {
        //     throw new Error('缺少必要的认证令牌(token)，请先调用getAccessToken获取');
        // }

        // 设置请求头
        const requestHeaders = {
            "Authentication": "Basic " + token,
            'Content-Type': isFormData ? 'application/x-www-form-urlencoded' : 'application/json',
            ...headers
        };

        // 构建请求配置
        const config = {
            url: url.startsWith('http') ? url : baseUrl + url,
            method: method.toUpperCase(),
            headers: requestHeaders,
            params: method.toUpperCase() === 'GET' ? params : undefined,
            data: method.toUpperCase() === 'POST' 
                ? (isFormData ? qs.stringify(data) : data)
                : undefined
        };

        console.log('API请求配置:', {
            url: config.url,
            method,
            params,
            data
        });

        // 发送请求
        const response = await axios(config);

        console.log('API响应数据:', {
            status: response.status,
            data: response.data
        });

        return {
            success: true,
            data: response.data,
            status: response.status,
            headers: response.headers
        };

    } catch (error) {
        console.error('API调用失败:', {
            url,
            method,
            error: error.message,
            response: error.response?.data
        });

        return {
            success: false,
            error: error.message,
            status: error.response?.status,
            data: error.response?.data
        };
    }
}

/**
 * GET请求方法
 * @param {string} url - 接口地址
 * @param {Object} params - URL参数
 * @param {Object} headers - 请求头
 * @param {string} token - 认证令牌，必须由调用者提供
 * @param {string} baseUrl - API基础URL，默认为API_CONFIG.BASE_URL
 * @returns {Promise<Object>} 响应数据
 */
async function get(url, params = {}, headers = {}, token, baseUrl = API_CONFIG.BASE_URL) {
    return callApi({
        url,
        method: 'GET',
        params,
        headers,
        token,
        baseUrl
    });
}

/**
 * POST请求方法
 * @param {string} url - 接口地址
 * @param {Object} data - 请求数据
 * @param {Object} headers - 请求头
 * @param {boolean} isFormData - 是否使用表单格式
 * @param {string} token - 认证令牌，必须由调用者提供
 * @param {string} baseUrl - API基础URL，默认为API_CONFIG.BASE_URL
 * @returns {Promise<Object>} 响应数据
 */
async function post(url, data = {}, headers = {}, isFormData = false, token, baseUrl = API_CONFIG.BASE_URL) {
    return callApi({
        url,
        method: 'POST',
        data,
        headers,
        isFormData,
        token,
        baseUrl
    });
}

module.exports = {
    callApi,
    get,
    post,
    getAccessToken,
    API_CONFIG
};